import { createUmi } from '@metaplex-foundation/umi-bundle-defaults';
import { createSignerFromWalletAdapter } from '@metaplex-foundation/umi-signer-wallet-adapters';
import {
  createFungible,
  mplTokenMetadata,
  TokenStandard
} from '@metaplex-foundation/mpl-token-metadata';
import {
  generateSigner,
  percentAmount,
  publicKey as umiPublic<PERSON>ey,
  some,
  signerIdentity
} from '@metaplex-foundation/umi';
import type { WalletAdapter } from '@solana/wallet-adapter-base';

export interface TokenMetadata {
  name: string;
  symbol: string;
  description: string;
  image?: File;
  decimals: number;
  supply: number;
  revokeAuthorities: boolean;
  // Additional metadata fields
  externalUrl?: string;
  attributes?: Array<{
    trait_type: string;
    value: string;
  }>;
}

export interface TokenCreationResult {
  mint: string;
  metadataUri: string;
  signature: string;
  imageUrl?: string;
}

class MetadataService {
  private umi: any;

  constructor() {
    // Initialize UMI with devnet
    try {
      this.umi = createUmi('https://api.devnet.solana.com')
        .use(mplTokenMetadata());
      console.log('UMI initialized successfully');
    } catch (error) {
      console.error('Failed to initialize UMI:', error);
      throw new Error('Failed to initialize Metaplex UMI');
    }
  }

  /**
   * Upload image to a free hosting service
   */
  async uploadImage(file: File): Promise<string> {
    try {
      // Using imgbb.com as a free image hosting service
      // You can get a free API key from https://api.imgbb.com/
      const formData = new FormData();
      formData.append('image', file);
      
      // For demo purposes, we'll use a public imgbb key (limited usage)
      // In production, users should provide their own API key
      const apiKey = '2d1f06433f39ac7c9ffc8a4ab8f99b77'; // Demo key - replace with your own
      
      const response = await fetch(`https://api.imgbb.com/1/upload?key=${apiKey}`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();
      return data.data.url;
    } catch (error) {
      console.error('Image upload failed:', error);
      // Fallback: return a placeholder image URL
      return 'https://via.placeholder.com/512x512/6366f1/ffffff?text=Token';
    }
  }

  /**
   * Upload metadata JSON to a free hosting service
   */
  async uploadMetadata(metadata: any): Promise<string> {
    try {
      // Using jsonbin.io as a free JSON hosting service
      const response = await fetch('https://api.jsonbin.io/v3/b', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Master-Key': '$2a$10$8VnzIgSQEHQVQc4Z8K9uAuIDO2QgTJ5VRgHJmFhMxEuCGQfHdgHSi', // Demo key
        },
        body: JSON.stringify(metadata),
      });

      if (!response.ok) {
        throw new Error('Failed to upload metadata');
      }

      const data = await response.json();
      return `https://api.jsonbin.io/v3/b/${data.metadata.id}/latest`;
    } catch (error) {
      console.error('Metadata upload failed:', error);
      // Fallback: create a simple metadata object
      const fallbackMetadata = {
        name: metadata.name,
        symbol: metadata.symbol,
        description: metadata.description,
        image: metadata.image || 'https://via.placeholder.com/512x512/6366f1/ffffff?text=Token'
      };
      
      // Ultimate fallback - return a data URL
      return `data:application/json;base64,${btoa(JSON.stringify(fallbackMetadata))}`;
    }
  }

  /**
   * Create a token with metadata using Metaplex
   */
  async createTokenWithMetadata(
    wallet: WalletAdapter,
    tokenData: TokenMetadata
  ): Promise<TokenCreationResult> {
    try {
      // Validate wallet connection
      if (!wallet.connected || !wallet.publicKey) {
        throw new Error('Wallet not connected');
      }

      // Set up UMI with wallet signer
      console.log('Setting up UMI with wallet signer...');
      let signer;
      try {
        signer = createSignerFromWalletAdapter(wallet);
        console.log('Signer created from wallet adapter');
        this.umi = this.umi.use(signerIdentity(signer));
        console.log('Signer configured successfully');
      } catch (signerError) {
        console.error('Failed to setup signer:', signerError);
        throw new Error(`Failed to setup wallet signer: ${signerError instanceof Error ? signerError.message : 'Unknown error'}`);
      }

      // Upload image if provided
      let imageUrl: string | undefined;
      if (tokenData.image) {
        imageUrl = await this.uploadImage(tokenData.image);
      }

      // Prepare metadata
      const metadata = {
        name: tokenData.name,
        symbol: tokenData.symbol,
        description: tokenData.description,
        image: imageUrl || 'https://via.placeholder.com/512x512/6366f1/ffffff?text=Token',
        external_url: tokenData.externalUrl || '',
        attributes: tokenData.attributes || [],
        properties: {
          files: imageUrl ? [
            {
              uri: imageUrl,
              type: 'image/png'
            }
          ] : [],
          category: 'image',
        },
        creators: [
          {
            address: wallet.publicKey?.toString() || '',
            verified: true,
            share: 100,
          },
        ],
      };

      // Upload metadata
      const metadataUri = await this.uploadMetadata(metadata);

      // Generate mint keypair
      const mint = generateSigner(this.umi);

      // Create the fungible token with metadata
      const createTokenInstruction = {
        mint,
        name: tokenData.name,
        symbol: tokenData.symbol,
        uri: metadataUri,
        sellerFeeBasisPoints: percentAmount(0, 2), // 0% royalty
        decimals: tokenData.decimals,
        amount: tokenData.supply * Math.pow(10, tokenData.decimals),
        tokenStandard: TokenStandard.Fungible,
        creators: some([
          {
            address: umiPublicKey(wallet.publicKey!.toString()),
            verified: true,
            share: 100,
          },
        ]),
        isMutable: !tokenData.revokeAuthorities,
        updateAuthority: tokenData.revokeAuthorities ? undefined : signer,
      };

      // Execute the transaction
      console.log('Executing token creation transaction...');
      try {
        const result = await createFungible(this.umi, createTokenInstruction).sendAndConfirm();
        console.log('Transaction completed successfully:', result.signature.toString());
        return {
          mint: mint.publicKey.toString(),
          metadataUri,
          signature: typeof result.signature === 'string' ? result.signature : result.signature.toString(),
          imageUrl,
        };
      } catch (transactionError) {
        console.error('Transaction execution failed:', transactionError);
        throw new Error(`Transaction failed: ${transactionError instanceof Error ? transactionError.message : 'Unknown transaction error'}`);
      }
    } catch (error) {
      console.error('Token creation failed:', error);
      throw new Error(`Failed to create token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate image file
   */
  validateImage(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return { valid: false, error: 'Please select an image file' };
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return { valid: false, error: 'Image must be smaller than 5MB' };
    }

    // Check dimensions (optional - we can resize if needed)
    return { valid: true };
  }

  /**
   * Get token metadata from URI
   */
  async getTokenMetadata(metadataUri: string): Promise<any> {
    try {
      const response = await fetch(metadataUri);
      if (!response.ok) {
        throw new Error('Failed to fetch metadata');
      }
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch token metadata:', error);
      return null;
    }
  }
}

export const metadataService = new MetadataService();